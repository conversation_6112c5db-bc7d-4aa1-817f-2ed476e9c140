import { ModelFactory } from '../../factories/ModelFactory.js';
import { MessageConverter } from '../../utils/MessageConverter.js';
import { ApiKeyError } from '../../errors/GeminiErrors.js';
import { DEFAULT_GEMINI_MODELS } from '../../config/constants.js';
import { langfuseManager } from '../../utils/LangfuseManager.js';

/**
 * Gemini provider management class
 */
export class GeminiProvider {
	constructor(apiKeyManager) {
		this.apiKeyManager = apiKeyManager;
	}

	/**
	 * Attempts completion with all available Gemini models and API keys
	 * @param {object} env - Environment variables
	 * @param {object} config - AI configuration object
	 * @param {Array} contents - Array of message contents
	 * @param {ErrorAggregator} errorAggregator - Error aggregator instance
	 * @returns {Promise<object|null>} Response object or null if all attempts fail
	 */
	async attemptCompletion(env, config, contents, errorAggregator) {
		// Get optimized Langfuse handler from centralized manager
		const langfuseHandler = langfuseManager.getHandler(env, config);

		const keys = this.apiKeyManager.loadGeminiApiKeys(env);

		// Determine the list of models to try. This is done once for efficiency.
		const modelsToTry = (config.model ? [config.model] : (env.GEMINI_MODELS || DEFAULT_GEMINI_MODELS).split(','))
			.map((m) => m.trim())
			.filter(Boolean);

		// Iterate through each API key.
		for (let keyIndex = 0; keyIndex < keys.length; keyIndex++) {
			const apiKey = this.apiKeyManager.getNextGeminiApiKey(env);
			if (!apiKey) {
				throw new ApiKeyError('No Gemini API key available.');
			}

			// For each key, iterate through the list of models to try.
			for (const modelName of modelsToTry) {
				try {
					// Attempt completion with the current model and API key.
					return await this._attemptModelCompletion(modelName, config, apiKey, contents, langfuseHandler);
				} catch (error) {
					// If it fails, log the error and continue to the next model/key.
					const context = `gemini/${modelName}`;
					console.error(`Failed chat completion with ${context}:`, error);
					errorAggregator.addError(context, error);
				}
			}
		}

		// All attempts with all keys and models failed.
		return null;
	}

	/**
	 * Attempts chat completion with a specific Gemini model
	 * @param {string} modelName - Name of the model to use
	 * @param {object} config - AI configuration object
	 * @param {string} apiKey - API key for authentication
	 * @param {Array} contents - Array of message contents
	 * @param {CallbackHandler|null} langfuseHandler - Optional Langfuse handler
	 * @returns {Promise<object>} Response object with text and thoughts
	 * @private
	 */
	async _attemptModelCompletion(modelName, config, apiKey, contents, langfuseHandler) {
		console.log(`Attempting chat completion with model: ${modelName}`);

		const agent = ModelFactory.createGeminiAgent(apiKey, modelName, config);
		const messages = MessageConverter.convertToLangChainMessages(contents);

		// Prepare invoke options with optional Langfuse callback
		const invokeOptions = {};
		if (langfuseHandler) {
			invokeOptions.callbacks = [langfuseHandler];
		}

		const result = await agent.invoke({ messages }, invokeOptions);

		const lastMessage = result.messages[result.messages.length - 1];
		const responseContent = lastMessage.content;

		console.log(`Successfully completed chat with model: ${modelName}`);

		return {
			text: responseContent,
			thoughts: '', // LangChain doesn't expose thoughts in the same way
		};
	}
}
