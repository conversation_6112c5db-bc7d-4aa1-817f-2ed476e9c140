/**
 * Manages user facts and insights
 * @module factManager
 */

import { extractAndStoreFacts, getUserFacts } from '../../redis.js';

/**
 * Prepares the user facts context and triggers fact extraction
 * @param {Object} env - Environment variables
 * @param {string|number} userId - The user ID
 * @param {string} currentMessageText - Current message text
 * @param {Array<Object>} previousMessages - Previous messages in the conversation
 * @returns {Promise<{factsString: string}>}
 */
export async function prepareFactContext(env, userId, currentMessageText, previousMessages) {
	try {
		// Get user facts
		const userFacts = await getUserFacts(env, userId);
		const factsString = userFacts.map(String).join('\n') || '';

		// Reduce previous messages to last 5 messages
		previousMessages = previousMessages.slice(-5);

		// Extract and store facts
		extractAndStoreFacts(env, userId, currentMessageText, previousMessages).catch((error) =>
			console.error('Error during fact extraction:', error)
		);

		console.log('Prepared Facts String:', factsString);

		return { factsString };
	} catch (error) {
		console.error('Error in prepareFactContext:', error);
		return { factsString: '' };
	}
}
