import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { secureHeaders } from 'hono/secure-headers';

// Import custom middleware
import { validationMiddleware, hostnameCheckMiddleware, errorHandlerMiddleware } from './middleware';

// Import application routes
import routes from './routes/index.js';

// Initialize Hono app
const app = new Hono();

// === Global Middleware Registration ===
// NOTE: Order matters!

// Error <PERSON> (registered first to wrap everything, but executes last on error)
app.use('*', errorHandlerMiddleware);

// Security Headers
app.use('*', secureHeaders());

// CORS Handling
app.use('*', cors(/* { origin: 'YOUR_FRONTEND_URL' } */));

// Hostname Check (apply early)
app.use('*', hostnameCheckMiddleware);

// Request Validation
app.use('*', validationMiddleware);

// === Route Mounting ===
app.route('/', routes);

// === Default Export for Cloudflare Worker ===
export default app;
