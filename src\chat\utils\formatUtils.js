/**
 * Format utilities for chat messages
 * @module formatUtils
 */

// Constants
const DATE_FORMAT_OPTIONS = {
	year: 'numeric',
	month: 'short',
	day: 'numeric',
	hour: '2-digit',
	minute: '2-digit',
};

/**
 * Formats a timestamp consistently across the application
 * @param {number} [timestamp] - Unix timestamp in seconds
 * @returns {string} Formatted date string
 */
export function formatTimestamp(timestamp, env) {
	return new Date((timestamp || 0) * 1000).toLocaleString('en-GB', {
		...DATE_FORMAT_OPTIONS,
		weekday: 'long',
		timeZone: env.TIMEZONE || 'UTC',
	});
}

/**
 * Formats a single message for display in chat history
 * @param {Object} messageData - The message data to format
 * @param {Object} env - Environment variables
 * @param {boolean} [summary=false] - Whether to use summary or full text
 * @returns {string} Formatted message string
 */
export function formatMessage(messageData, env, summary) {
	const formattedTimestamp = formatTimestamp(messageData.timestamp || messageData.date, env);

	const isBot = messageData.role === 'assistant' || messageData.from?.is_bot;
	const textContent = isBot && summary ? messageData.summary : messageData.text || '';
	const tagName = isBot ? 'HARMONY_RESPONSE' : 'USER_MESSAGE';

	if (!isBot) {
		return `<${tagName} date="${formattedTimestamp}" user_id="${messageData.userId || messageData.from?.id}">${textContent}</${tagName}>`;
	} else {
		return `<${tagName} date="${formattedTimestamp}">${textContent}</${tagName}>`;
	}
}

/**
 * Formats the current user message
 * @param {string} messageText - The current message text
 * @param {string} senderName - Name of the message sender
 * @returns {string} Formatted current message
 */
export function formatCurrentMessage(messageText) {
	return `<USER_MESSAGE>${messageText}</USER_MESSAGE>`;
}
