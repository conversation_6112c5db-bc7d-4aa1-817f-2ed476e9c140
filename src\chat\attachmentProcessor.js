import { getMediaGroup } from '../redis.js';
import { fetchAndProcessFile } from './fileProcessor.js';
import { ALLOWED_FILE_TYPES } from '../constants.js';

/**
 * Processes attachments from a message, handling replies, media groups, and current message attachments.
 * @param {object} env - The environment variables.
 * @param {object} messageData - The message data object.
 * @returns {Promise<Array<object>>} - A promise that resolves to an array of attachment parts.
 */
export async function processAttachments(env, messageData) {
	const { replyToPhoto, replyToDocument, mediaGroupId, photo, document } = messageData;
	const botToken = env.HRMNY_BOT_TOKEN;
	let attachmentsToProcess = [];

	if (replyToPhoto && replyToPhoto.length > 0) {
		console.log('Processing replied-to photo...');
		const highestResPhoto = replyToPhoto[replyToPhoto.length - 1];
		attachmentsToProcess.push({ file_id: highestResPhoto.file_id, mime_type: 'image/jpeg' });
	} else if (replyToDocument && replyToDocument.file_id) {
		console.log('Processing replied-to document...');
		attachmentsToProcess.push({ file_id: replyToDocument.file_id, mime_type: replyToDocument.mime_type });
	} else if (mediaGroupId) {
		console.log(`Processing media group with ID: ${mediaGroupId}`);
		await new Promise((resolve) => setTimeout(resolve, 1500)); // Delay to ensure all media is received
		try {
			const mediaGroupResult = await getMediaGroup(env, mediaGroupId);
			if (Array.isArray(mediaGroupResult)) {
				attachmentsToProcess = mediaGroupResult.map((item) => ({
					file_id: item.file_id,
					mime_type: item.type === 'photo' ? 'image/jpeg' : item.mime_type,
				}));
			}
		} catch (e) {
			console.error(`Error getting media group ${mediaGroupId}:`, e);
		}
	} else if (photo && photo.length > 0) {
		console.log('Processing photo from current message...');
		const highestResPhoto = photo[photo.length - 1];
		attachmentsToProcess.push({ file_id: highestResPhoto.file_id, mime_type: 'image/jpeg' });
	} else if (document && document.file_id) {
		console.log('Processing document from current message...');
		attachmentsToProcess.push({ file_id: document.file_id, mime_type: document.mime_type });
	}

	if (attachmentsToProcess.length === 0) {
		return [];
	}

	const processPromises = attachmentsToProcess.map((attachment) =>
		fetchAndProcessFile(botToken, attachment.file_id, attachment.mime_type, ALLOWED_FILE_TYPES)
	);

	const processedAttachments = await Promise.all(processPromises);
	return processedAttachments.filter(Boolean); // Filter out any null values
}
