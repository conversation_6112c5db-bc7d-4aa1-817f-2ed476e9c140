import { callFastGenerativeAI } from '../../chat/geminiAI.js';
import { formatRedisHistory } from '../../chat/history/historyManager.js';

export class FactExtractor {
	constructor(env) {
		this.env = env;
	}

	async extractFacts(userId, text, messageHistory) {
		const { EXTRACTION_PROMPT, EXTRACTION_SYSTEM_PROMPT } = await import('../../constants.js');

		const currentDate = new Date().toLocaleString('en-US', { timeZone: this.env.TIMEZONE });
		const context = formatRedisHistory(messageHistory, this.env, true);
		const prompt = EXTRACTION_PROMPT.replace('{CURRENT_DATE}', currentDate)
			.replace('{USER_ID}', userId)
			.replace('{USER_MESSAGE}', text)
			.replace('{HISTORY}', context);

		const response = await this._callAI(prompt, EXTRACTION_SYSTEM_PROMPT);
		const responseText = this._extractResponseText(response.text);

		return this._parseFacts(responseText, userId);
	}

	async _callAI(prompt, systemPrompt) {
		const temperature = 0.1;

		const config = {
			temperature: temperature,
			systemInstruction: systemPrompt,
			inferenceProvider: 'groq',
			model: 'moonshotai/kimi-k2-instruct',
			traceTags: ['fact-extract'],
		};

		const contents = [{ role: 'user', parts: [{ text: prompt }] }];
		return callFastGenerativeAI(this.env, config, contents);
	}

	_extractResponseText(response) {
		if (!response) return '';

		if (Array.isArray(response)) {
			const last = response[response.length - 1];
			return last?.text.trim() || '';
		}

		return typeof response === 'string' ? response.trim() : '';
	}

	_parseFacts(response, userId) {
		if (!response) {
			console.warn(`[FactExtractor] No response from AI for user ${userId}`);
			return [];
		}

		if (response.toUpperCase() === 'NO_FACTS_FOUND') {
			console.log(`[FactExtractor] No facts found for ${userId}`);
			return [];
		}

		const facts = response
			.split('\n')
			.map((fact) => fact.replace(/^[-•*]\s*/, '').replace(/^\d+\.\s*/, ''))
			.filter((fact) => fact.length > 5);

		console.log(`[FactExtractor] Extracted ${facts.length} facts for ${userId}`);
		return facts;
	}
}
