import { Hono } from 'hono';
import { logTelegramMessage } from '../redis.js';
import { processTelegramMessageInBackground } from '../chat/messageProcessor.js';
import { parseWebhookData, isValidWebhookData, extractMessageDetails, handleInitialError } from '../utils/telegramWebhookHelpers.js';
import { storeMessageEmbedding } from '../utils/vectorStorage.js';
import { detectCommand } from '../chat/commandHandler.js';
import { MonologueService } from '../chat/MonologueService.js';
import { sendTelegramMessage, sendTelegramError } from '../telegram.js';
import { escapeMdV2 } from '../chat/telegramUtils.js';

const routes = new Hono();

// Health Check Endpoint
routes.get('/health', (c) => {
	return c.json({
		status: 'healthy',
		timestamp: new Date().toISOString(),
		environment: c.env.DEV_MODE === 'true' ? 'development' : 'production',
	});
});

routes.post('/health', (c) => {
	return c.json({
		status: 'healthy',
		timestamp: new Date().toISOString(),
		environment: c.env.DEV_MODE === 'true' ? 'development' : 'production',
	});
});

// Root Redirect
routes.get('/', (c) => {
	// Redirect to the project's GitHub repository or a documentation page
	return c.redirect('https://github.com/hudzax/amai-lyrics', 302); // Use 302 for temporary redirect
});

// Simple Ping Endpoint
routes.get('/ping', (c) => c.text('pong'));

// Handle OPTIONS requests for CORS preflight
routes.options('*', (c) => {
	// CORS headers should be handled by the global cors middleware
	// This handler just needs to return OK for the preflight request
	return c.text('', 204); // 204 No Content is standard for OPTIONS preflight success
});

// === Telegram Webhook Endpoint for HRMNY ===
routes.post('/hrmny', async (c) => {
	try {
		const webhookData = await parseWebhookData(c.req);
		console.log('[Telegram Webhook] Received data:', JSON.stringify(webhookData));

		if (!isValidWebhookData(webhookData)) {
			console.warn('[Telegram Webhook] Invalid data: missing message or callback_query');
			return c.json({ success: true, message: 'Webhook received, nothing to process' });
		}

		const { chatId, text, photo = [], document = {}, messageDataForLogging } = extractMessageDetails(c.env, webhookData);

		// If nothing to log or process, return early
		if (!chatId || !messageDataForLogging || (!text && photo.length === 0 && !document?.file_id)) {
			console.info('[Telegram Webhook] No content to log or process for chat:', chatId);
			return c.json({ success: true, message: 'Webhook received, nothing to process' });
		}

		// Store message if it's not a command
		const StoreMessage = async (text) => {
			if (!detectCommand(text)) {
				// Log the message to Redis for later processing
				await logTelegramMessage(c.env, chatId, messageDataForLogging).catch((redisError) => {
					console.error('[Telegram Webhook] Redis logging error:', redisError);
				});

				// Store to vector database for future processing
				await storeMessageEmbedding(c.env, webhookData, text, chatId).catch((vectorError) => {
					console.error('[Telegram Webhook] Vector storage error:', vectorError);
				});
			}
		};

		// Schedule all async operations to run in parallel
		c.executionCtx.waitUntil(
			Promise.all([
				StoreMessage(text),
				// Process the message in the background
				processTelegramMessageInBackground(c.env, webhookData).catch((err) => {
					console.error('[Telegram Webhook] Background processing error:', err);
				}),
			])
		);

		console.log('[Telegram Webhook] Scheduled logging and background processing for chat:', chatId);

		// Immediately acknowledge receipt to Telegram
		return c.json({ success: true, message: 'Webhook received' });
	} catch (error) {
		await handleInitialError(c, error);
		return c.json({ success: false, error: 'Internal server error' }, 500);
	}
});

// === AI Thoughts/Monologue Endpoint ===
routes.post('/thoughts', async (c) => {
	try {
		console.log('[Thoughts Endpoint] Received request for AI monologue generation');

		// Initialize the monologue service
		const monologueService = new MonologueService();

		// Check if enough time has passed since last monologue (rate limiting)
		const canGenerate = await monologueService.canGenerateMonologue(c.env);
		if (!canGenerate) {
			console.log('[Thoughts Endpoint] Rate limit: Not enough time since last monologue');

			// Log rate limiting for monitoring
			const rateLimitContext = {
				path: '/thoughts',
				method: 'POST',
				type: 'rate_limit',
				timestamp: new Date().toISOString(),
			};
			console.warn('[Thoughts Endpoint] Rate limit triggered:', rateLimitContext);

			return c.json(
				{
					success: false,
					error: 'Rate limited: Please wait before generating another monologue',
					nextAllowedTime: 'in a few minutes',
					retryAfter: 480, // 8 minutes in seconds
				},
				429
			);
		}

		// Generate the AI monologue
		console.log('[Thoughts Endpoint] Generating AI monologue...');
		const monologue = await monologueService.generateMonologue(c.env);

		if (!monologue || typeof monologue !== 'string') {
			console.error('[Thoughts Endpoint] Failed to generate valid monologue');
			return c.json(
				{
					success: false,
					error: 'Failed to generate valid monologue',
				},
				500
			);
		}

		// Send the monologue to the specified Telegram chat
		const targetChatId = '-4693815869'; // Specified chat ID
		console.log(`[Thoughts Endpoint] Sending monologue to chat ${targetChatId}`);

		// Escape the monologue text for MarkdownV2
		const escapedMonologue = escapeMdV2(monologue);
		const telegramResponse = await sendTelegramMessage(c.env, targetChatId, escapedMonologue);

		if (!telegramResponse) {
			console.error('[Thoughts Endpoint] Failed to send message to Telegram');

			// Send error notification to admin (async, don't block response)
			const telegramErrorContext = {
				path: '/thoughts',
				method: 'POST',
				type: 'telegram_send_failure',
				chatId: targetChatId,
				monologueLength: monologue?.length || 0,
			};

			// Use waitUntil to ensure error notification is sent
			c.executionCtx.waitUntil(sendTelegramError(c.env, new Error('Failed to send monologue to Telegram'), telegramErrorContext));

			return c.json(
				{
					success: false,
					error: 'Generated monologue but failed to send to Telegram',
					monologue: monologue, // Include the generated content for debugging
					chatId: targetChatId,
				},
				500
			);
		}

		console.log('[Thoughts Endpoint] Successfully sent monologue to Telegram');

		// Return success response
		return c.json({
			success: true,
			message: 'AI monologue generated and sent successfully',
			chatId: targetChatId,
			timestamp: new Date().toISOString(),
			telegramMessageId: telegramResponse.result?.message_id,
		});
	} catch (error) {
		console.error('[Thoughts Endpoint] Error:', error);

		// Log error context for debugging
		const errorContext = {
			path: '/thoughts',
			method: 'POST',
			error: error.message,
			stack: error.stack,
			timestamp: new Date().toISOString(),
			chatId: '-4693815869',
		};
		console.error('[Thoughts Endpoint] Error context:', errorContext);

		// Send error notification to admin (async, don't block response)
		c.executionCtx.waitUntil(
			sendTelegramError(c.env, error, errorContext).catch((notificationError) => {
				console.error('[Thoughts Endpoint] Failed to send error notification:', notificationError);
			})
		);

		// Determine appropriate status code
		const statusCode = error.status || error.statusCode || 500;
		const isClientError = statusCode >= 400 && statusCode < 500;

		return c.json(
			{
				success: false,
				error: isClientError ? error.message : 'Internal server error during monologue generation',
				timestamp: new Date().toISOString(),
				...(c.env.DEV_MODE === 'true' && { stack: error.stack }), // Include stack in dev mode
			},
			statusCode
		);
	}
});

export default routes;
